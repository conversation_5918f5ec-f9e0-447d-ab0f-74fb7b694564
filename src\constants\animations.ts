import type { AnimationConfig } from '@/types';

/**
 * CENTRIS ANIMATION - PRESNE PODĽA REFERENČNÉHO OBRÁZKA
 *
 * Vytvorené na základe druhého obrázka:
 * - Svetlé pozadie
 * - Floating document cards
 * - Geometric shapes v pastelových farbách
 * - Clean, modern look
 */
export const defaultAnimationConfig: AnimationConfig = {
  scene: {
    background: '#ffffff', // Čisto biele pozadie
    fog: {
      color: '#f8fafc',
      near: 30,
      far: 60,
    },
  },

  camera: {
    position: [0, 0, 20], // Vzdialenejšia kamera pre lepší prehľad
    fov: 50,
    near: 0.1,
    far: 100,
  },

  lights: [
    // Soft lighting pre clean look
    {
      type: 'ambient',
      color: '#ffffff',
      intensity: 1.0,
    },
    // Jemné smerovanie svetlo
    {
      type: 'directional',
      color: '#ffffff',
      intensity: 0.5,
      position: [10, 10, 5],
      castShadow: false,
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 1
    {
      type: 'plane',
      position: [-8.2, 2.9, 1.02],
      scale: [2.8, 0.08, 0.01],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
    },
    {
      type: 'plane',
      position: [-8.4, 2.6, 1.02],
      scale: [2.2, 0.06, 0.01],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 2
    {
      type: 'plane',
      position: [10.1, 1.1, -0.98],
      scale: [2.4, 0.07, 0.01],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
    },
    {
      type: 'plane',
      position: [9.9, 0.8, -0.98],
      scale: [1.8, 0.06, 0.01],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 3
    {
      type: 'plane',
      position: [2.1, -7.1, 2.02],
      scale: [2.6, 0.07, 0.01],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
    },
    {
      type: 'plane',
      position: [1.9, -7.4, 2.02],
      scale: [2, 0.06, 0.01],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 1
    {
      type: 'plane',
      position: [-8.2, 2.9, 1.02],
      scale: [2.8, 0.08, 0.01],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
    },
    {
      type: 'plane',
      position: [-8.4, 2.6, 1.02],
      scale: [2.2, 0.06, 0.01],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 2
    {
      type: 'plane',
      position: [10.1, 1.1, -0.98],
      scale: [2.4, 0.07, 0.01],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
    },
    {
      type: 'plane',
      position: [9.9, 0.8, -0.98],
      scale: [1.8, 0.06, 0.01],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
    },

    // TEXTOVÉ ČIARKY PRE DOCUMENT CARD 3
    {
      type: 'plane',
      position: [2.1, -7.1, 2.02],
      scale: [2.6, 0.07, 0.01],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
    },
    {
      type: 'plane',
      position: [1.9, -7.4, 2.02],
      scale: [2, 0.06, 0.01],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#e5e7eb',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
    },
  ],

  objects: [
    // DOCUMENT CARD 1 - Modrá karta (vľavo hore)
    // Modrá hlavička
    {
      type: 'plane',
      position: [-8, 4, 1],
      scale: [3.5, 1.8, 0.05],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#bfdbfe', // Svetlo modrá hlavička
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.9,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
      castShadow: false,
    },
    // Biely spodok
    {
      type: 'plane',
      position: [-8, 2.8, 1.01],
      scale: [3.5, 2.2, 0.05],
      rotation: [0, 0.2, -0.05],
      material: {
        color: '#ffffff',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.95,
      },
      animation: {
        type: 'float',
        speed: 0.4,
        amplitude: 0.6,
        offset: 0,
      },
      castShadow: false,
    },

    // DOCUMENT CARD 2 - Fialová karta (vpravo hore)
    // Fialová hlavička
    {
      type: 'plane',
      position: [10, 2, -1],
      scale: [3, 1.5, 0.05],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#e9d5ff', // Svetlo fialová hlavička
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.9,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
      castShadow: false,
    },
    // Biely spodok
    {
      type: 'plane',
      position: [10, 1, -0.99],
      scale: [3, 1.8, 0.05],
      rotation: [0, -0.3, 0.1],
      material: {
        color: '#ffffff',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.95,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 3,
      },
      castShadow: false,
    },

    // DOCUMENT CARD 3 - Zelená karta (dole stred)
    // Zelená hlavička
    {
      type: 'plane',
      position: [2, -6, 2],
      scale: [3.2, 1.6, 0.05],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#bbf7d0', // Svetlo zelená hlavička
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.9,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
      castShadow: false,
    },
    // Biely spodok
    {
      type: 'plane',
      position: [2, -7.2, 2.01],
      scale: [3.2, 2, 0.05],
      rotation: [0, 0.1, 0.08],
      material: {
        color: '#ffffff',
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.95,
      },
      animation: {
        type: 'float',
        speed: 0.3,
        amplitude: 0.5,
        offset: Math.PI / 2,
      },
      castShadow: false,
    },

    // BLUE CIRCLE - Modrý kruh (vpravo hore)
    {
      type: 'sphere',
      position: [10, 8, 0],
      scale: [2.5, 2.5, 2.5],
      material: {
        color: '#dbeafe', // Svetlo modrá
        metalness: 0.0,
        roughness: 0.3,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'drift',
        speed: 0.2,
        amplitude: 1.0,
        offset: 0,
      },
    },

    // ORANGE CIRCLE - Oranžový kruh (vľavo hore)
    {
      type: 'sphere',
      position: [-8, 6, 1],
      scale: [1.8, 1.8, 1.8],
      material: {
        color: '#fed7aa', // Svetlo oranžová
        metalness: 0.0,
        roughness: 0.3,
        transparent: true,
        opacity: 0.7,
      },
      animation: {
        type: 'drift',
        speed: 0.25,
        amplitude: 0.8,
        offset: Math.PI / 3,
      },
    },

    // PURPLE SQUARE - Fialový štvorec (vpravo dole)
    {
      type: 'box',
      position: [12, -6, -1],
      scale: [3, 3, 0.5],
      rotation: [0, 0, Math.PI / 6],
      material: {
        color: '#e9d5ff', // Svetlo fialová
        metalness: 0.0,
        roughness: 0.4,
        transparent: true,
        opacity: 0.75,
      },
      animation: {
        type: 'float',
        speed: 0.35,
        amplitude: 0.7,
        offset: Math.PI / 4,
      },
    },

    // GREEN SQUARE - Zelený štvorec (vľavo dole)
    {
      type: 'box',
      position: [-10, -8, 2],
      scale: [2.5, 2.5, 0.4],
      rotation: [0, 0, -Math.PI / 8],
      material: {
        color: '#bbf7d0', // Svetlo zelená
        metalness: 0.0,
        roughness: 0.4,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'drift',
        speed: 0.3,
        amplitude: 0.6,
        offset: Math.PI / 2,
      },
    },

    // SMALL BLUE CIRCLE - Malý modrý kruh (stred vpravo)
    {
      type: 'sphere',
      position: [15, 0, 3],
      scale: [1.2, 1.2, 1.2],
      material: {
        color: '#bfdbfe', // Stredne svetlo modrá
        metalness: 0.0,
        roughness: 0.3,
        transparent: true,
        opacity: 0.85,
      },
      animation: {
        type: 'float',
        speed: 0.5,
        amplitude: 0.4,
        offset: Math.PI / 6,
      },
    },

    // GRAY TRIANGLE - Šedý trojuholník (dole stred)
    {
      type: 'box', // Použijem rotovaný box namiesto cone
      position: [2, -10, 0],
      scale: [1.5, 1.5, 0.3],
      rotation: [0, 0, Math.PI / 4],
      material: {
        color: '#f1f5f9', // Svetlo šedá
        metalness: 0.0,
        roughness: 0.4,
        transparent: true,
        opacity: 0.6,
      },
      animation: {
        type: 'rotate',
        speed: 0.15,
        axis: 'z',
      },
    },

    // YELLOW SHAPE - Žltý tvar (hore stred)
    {
      type: 'box',
      position: [0, 10, -2],
      scale: [2, 1, 0.4],
      rotation: [0, Math.PI / 3, 0],
      material: {
        color: '#fef3c7', // Svetlo žltá
        metalness: 0.0,
        roughness: 0.4,
        transparent: true,
        opacity: 0.7,
      },
      animation: {
        type: 'drift',
        speed: 0.4,
        amplitude: 0.5,
        offset: Math.PI / 8,
      },
    },

    // PINK CIRCLE - Ružový kruh (vľavo stred)
    {
      type: 'sphere',
      position: [-15, 2, -1],
      scale: [1.5, 1.5, 1.5],
      material: {
        color: '#fce7f3', // Svetlo ružová
        metalness: 0.0,
        roughness: 0.3,
        transparent: true,
        opacity: 0.75,
      },
      animation: {
        type: 'scale',
        speed: 0.6,
        amplitude: 0.2,
        offset: Math.PI / 5,
      },
    },

    // CYAN SHAPE - Cyan tvar (stred)
    {
      type: 'torus',
      position: [4, 4, 1],
      scale: [1.5, 1.5, 1.5],
      rotation: [Math.PI / 2, 0, 0],
      material: {
        color: '#cffafe', // Svetlo cyan
        metalness: 0.0,
        roughness: 0.3,
        transparent: true,
        opacity: 0.8,
      },
      animation: {
        type: 'rotate',
        speed: 0.3,
        axis: 'y',
      },
    },

    // PROCESSING INDICATOR - Malý modrý indikátor
    {
      type: 'sphere',
      position: [6, 1, 4],
      scale: [0.8, 0.8, 0.8],
      material: {
        color: '#3b82f6',
        metalness: 0.3,
        roughness: 0.2,
        emissive: '#3b82f6',
        emissiveIntensity: 0.1,
      },
      animation: {
        type: 'orbit',
        speed: 1.0,
        amplitude: 1.5,
        offset: 0,
      },
    },
  ],

  postProcessing: {
    bloom: {
      enabled: false,
      strength: 0,
      radius: 0,
      threshold: 0,
    },
  },

  physics: {
    enabled: false,
    gravity: [0, -9.81, 0],
    objects: [],
  },

  performance: {
    antialias: true,
    shadowMapEnabled: false,
    pixelRatio:
      typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 2) : 1,
    powerPreference: 'high-performance',
  },
};
