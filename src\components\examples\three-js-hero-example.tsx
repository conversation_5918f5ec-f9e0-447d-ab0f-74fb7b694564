'use client';

import React from 'react';
import ThreeJsHero from '@/components/three-js-hero';
import {
  defaultAnimationConfig,
  aboutAnimationConfig,
} from '@/constants/animations';

/**
 * Example usage of ThreeJsHero component with i18next internationalization
 *
 * This component demonstrates:
 * - Multilingual support using react-i18next
 * - Different animation configurations for different contexts
 * - Proper integration with the project's design system
 * - Responsive design and accessibility considerations
 */

// Homepage Hero Example
export const HomepageHeroExample = () => {
  return (
    <ThreeJsHero
      animationConfig={defaultAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="min-h-screen"
    />
  );
};

// About Page Hero Example
export const AboutHeroExample = () => {
  return (
    <ThreeJsHero
      animationConfig={aboutAnimationConfig}
      enableControls={true}
      autoRotate={true}
      showStats={false}
      className="h-[80vh]"
    />
  );
};

// Products Page Hero Example - Centris Document Processing System
export const ProductsHeroExample = () => {
  // Enhanced animation config specifically for Centris document processing
  const centrisAnimationConfig = {
    ...defaultAnimationConfig,
    camera: {
      position: [0, 2, 8] as [number, number, number],
      fov: 70,
    },
    objects: [
      // Central Data Core - represents the heart of Centris system
      defaultAnimationConfig.objects[0],
      // Document Processing Rings - represent workflow stages
      defaultAnimationConfig.objects[1],
      // Data Flow Cubes - represent document types
      defaultAnimationConfig.objects[2],
      defaultAnimationConfig.objects[3],
      // Processing Nodes - system modules
      defaultAnimationConfig.objects[4],
      defaultAnimationConfig.objects[5],
    ],
    particles: [
      // Enhanced data stream particles for document flow visualization
      {
        ...defaultAnimationConfig.particles![0],
        count: 150,
        animation: {
          type: 'flow' as const,
          speed: 0.8,
          direction: [0, 1, 0] as [number, number, number],
        },
      },
      // Processing particles representing active document processing
      {
        ...defaultAnimationConfig.particles![1],
        count: 80,
        animation: {
          type: 'spiral' as const,
          speed: 1.2,
        },
      },
    ],
  };

  return (
    <ThreeJsHero
      animationConfig={centrisAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="h-screen"
    />
  );
};

// Interactive Demo Example (with controls enabled)
export const InteractiveDemoExample = () => {
  // Enhanced animation config for interactive demo
  const demoAnimationConfig = {
    ...defaultAnimationConfig,
    performance: {
      ...defaultAnimationConfig.performance,
      shadowMapEnabled: true,
      antialias: true,
    },
  };

  return (
    <div className="relative">
      <ThreeJsHero
        animationConfig={demoAnimationConfig}
        enableControls={true}
        autoRotate={true}
        showStats={true}
        className="h-screen"
      />

      {/* Additional UI overlay for demo */}
      <div className="bg-card/90 absolute top-4 right-4 z-20 max-w-sm rounded-lg p-4 backdrop-blur-sm">
        <h3 className="text-card-foreground mb-2 font-semibold">
          Interactive Demo
        </h3>
        <ul className="text-muted-foreground space-y-1 text-sm">
          <li>• Drag to rotate the scene</li>
          <li>• Scroll to zoom in/out</li>
          <li>• Watch the animated elements</li>
          <li>• Experience real-time 3D graphics</li>
        </ul>
      </div>
    </div>
  );
};

// Minimal Hero Example (for performance-critical pages)
export const MinimalHeroExample = () => {
  // Minimal animation config for better performance
  const minimalAnimationConfig = {
    scene: {
      background: 'hsl(var(--background))',
    },
    camera: {
      position: [0, 0, 5] as [number, number, number],
      fov: 75,
    },
    lights: [
      {
        type: 'ambient' as const,
        color: 'hsl(var(--foreground))',
        intensity: 0.6,
      },
      {
        type: 'directional' as const,
        color: 'hsl(var(--primary))',
        intensity: 1,
        position: [2, 2, 2] as [number, number, number],
      },
    ],
    objects: [
      {
        type: 'sphere' as const,
        position: [0, 0, 0] as [number, number, number],
        material: {
          color: 'hsl(var(--primary))',
          metalness: 0.7,
          roughness: 0.3,
        },
        animation: {
          type: 'rotate' as const,
          speed: 0.5,
          axis: 'y' as const,
        },
      },
    ],
    performance: {
      antialias: false,
      shadowMapEnabled: false,
      pixelRatio: 1,
    },
  };

  return (
    <ThreeJsHero
      animationConfig={minimalAnimationConfig}
      enableControls={false}
      autoRotate={false}
      showStats={false}
      className="h-[60vh]"
    />
  );
};

// Export all examples for easy importing
export const ThreeJsHeroExamples = {
  Homepage: HomepageHeroExample,
  About: AboutHeroExample,
  Products: ProductsHeroExample,
  InteractiveDemo: InteractiveDemoExample,
  Minimal: MinimalHeroExample,
};
