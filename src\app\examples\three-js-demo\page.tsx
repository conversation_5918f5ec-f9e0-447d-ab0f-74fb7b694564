import React from 'react';
import { Metadata } from 'next';
import {
  HomepageHeroExample,
  AboutHeroExample,
  ProductsHeroExample,
  InteractiveDemoExample,
  MinimalHeroExample,
} from '@/components/examples/three-js-hero-example';

export const metadata: Metadata = {
  title: 'Three.js Hero Component Demo - Centris',
  description:
    'Interactive demonstration of the ThreeJsHero component with various configurations and animations.',
  keywords: [
    'Three.js',
    'React',
    'Hero Component',
    'Centris',
    '3D Animation',
    'WebGL',
  ],
};

/**
 * Demo page showcasing different configurations of the ThreeJsHero component
 *
 * This page demonstrates:
 * - Multiple hero configurations
 * - Performance considerations
 * - Responsive design
 * - Accessibility features
 * - Internationalization support
 */
export default function ThreeJsDemoPage() {
  return (
    <main className="bg-background min-h-screen">
      {/* Page Header */}
      <section className="bg-card border-b">
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-foreground mb-4 text-4xl font-bold">
            ThreeJS Hero Component Demo
          </h1>
          <p className="text-muted-foreground max-w-3xl text-lg">
            Explore different configurations of our advanced 3D hero component.
            Each example demonstrates different features, performance
            optimizations, and use cases for the Centris document processing
            system.
          </p>
        </div>
      </section>

      {/* Demo Sections */}
      <div className="space-y-16">
        {/* Homepage Hero Demo */}
        <section id="homepage-demo">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
              <h2 className="text-foreground mb-2 text-2xl font-semibold">
                1. Homepage Hero (Default Configuration)
              </h2>
              <p className="text-muted-foreground">
                Full-featured hero with advanced animations, particle systems,
                and Centris branding. Optimized for maximum visual impact while
                maintaining performance.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
                  Advanced Animations
                </span>
                <span className="bg-secondary/10 text-secondary rounded-full px-3 py-1 text-sm">
                  Particle Systems
                </span>
                <span className="bg-accent/10 text-accent rounded-full px-3 py-1 text-sm">
                  Bloom Effects
                </span>
              </div>
            </div>
          </div>
          <HomepageHeroExample />
        </section>

        {/* About Hero Demo */}
        <section id="about-demo">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
              <h2 className="text-foreground mb-2 text-2xl font-semibold">
                2. About Page Hero (Interactive Controls)
              </h2>
              <p className="text-muted-foreground">
                Simplified animation with interactive controls enabled. Users
                can rotate and zoom the 3D scene. Perfect for engaging about
                pages and company presentations.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="bg-chart-1/10 text-chart-1 rounded-full px-3 py-1 text-sm">
                  Interactive Controls
                </span>
                <span className="bg-chart-2/10 text-chart-2 rounded-full px-3 py-1 text-sm">
                  Auto Rotation
                </span>
                <span className="bg-chart-3/10 text-chart-3 rounded-full px-3 py-1 text-sm">
                  Simplified Scene
                </span>
              </div>
            </div>
          </div>
          <AboutHeroExample />
        </section>

        {/* Products Hero Demo */}
        <section id="products-demo">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
              <h2 className="text-foreground mb-2 text-2xl font-semibold">
                3. Products Page Hero (Custom Configuration)
              </h2>
              <p className="text-muted-foreground">
                Customized animation configuration for product pages. Reduced
                particle count and optimized camera positioning for better focus
                on content.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm">
                  Custom Camera
                </span>
                <span className="bg-secondary/10 text-secondary rounded-full px-3 py-1 text-sm">
                  Reduced Particles
                </span>
                <span className="bg-accent/10 text-accent rounded-full px-3 py-1 text-sm">
                  Product Focus
                </span>
              </div>
            </div>
          </div>
          <ProductsHeroExample />
        </section>

        {/* Interactive Demo */}
        <section id="interactive-demo">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
              <h2 className="text-foreground mb-2 text-2xl font-semibold">
                4. Interactive Demo (Full Controls + Stats)
              </h2>
              <p className="text-muted-foreground">
                Complete interactive experience with performance statistics,
                orbit controls, and enhanced visual effects. Ideal for
                showcasing technical capabilities.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="bg-chart-1/10 text-chart-1 rounded-full px-3 py-1 text-sm">
                  Performance Stats
                </span>
                <span className="bg-chart-2/10 text-chart-2 rounded-full px-3 py-1 text-sm">
                  Full Controls
                </span>
                <span className="bg-chart-3/10 text-chart-3 rounded-full px-3 py-1 text-sm">
                  Enhanced Effects
                </span>
              </div>
            </div>
          </div>
          <InteractiveDemoExample />
        </section>

        {/* Minimal Hero Demo */}
        <section id="minimal-demo">
          <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
              <h2 className="text-foreground mb-2 text-2xl font-semibold">
                5. Minimal Hero (Performance Optimized)
              </h2>
              <p className="text-muted-foreground">
                Lightweight version optimized for performance-critical pages.
                Single object, no particles, disabled shadows and antialiasing
                for maximum frame rate.
              </p>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="rounded-full bg-green-500/10 px-3 py-1 text-sm text-green-600">
                  High Performance
                </span>
                <span className="rounded-full bg-blue-500/10 px-3 py-1 text-sm text-blue-600">
                  Low Memory
                </span>
                <span className="rounded-full bg-purple-500/10 px-3 py-1 text-sm text-purple-600">
                  Mobile Optimized
                </span>
              </div>
            </div>
          </div>
          <MinimalHeroExample />
        </section>
      </div>

      {/* Technical Information */}
      <section className="bg-muted/30 border-t">
        <div className="container mx-auto px-4 py-12">
          <h2 className="text-foreground mb-8 text-center text-3xl font-bold">
            Technical Specifications
          </h2>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Performance */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-foreground mb-4 text-xl font-semibold">
                Performance Features
              </h3>
              <ul className="text-muted-foreground space-y-2">
                <li>• Automatic device pixel ratio optimization</li>
                <li>• Configurable shadow mapping</li>
                <li>• Efficient particle systems</li>
                <li>• Memory management and cleanup</li>
                <li>• Level-of-detail (LOD) support</li>
              </ul>
            </div>

            {/* Accessibility */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-foreground mb-4 text-xl font-semibold">
                Accessibility
              </h3>
              <ul className="text-muted-foreground space-y-2">
                <li>• WCAG 2.1 AA compliant</li>
                <li>• Reduced motion support</li>
                <li>• Keyboard navigation</li>
                <li>• Screen reader compatibility</li>
                <li>• High contrast mode support</li>
              </ul>
            </div>

            {/* Browser Support */}
            <div className="bg-card rounded-lg border p-6">
              <h3 className="text-foreground mb-4 text-xl font-semibold">
                Browser Support
              </h3>
              <ul className="text-muted-foreground space-y-2">
                <li>• Chrome 80+ ✓</li>
                <li>• Firefox 75+ ✓</li>
                <li>• Safari 13+ ✓</li>
                <li>• Edge 80+ ✓</li>
                <li>• WebGL 1.0+ required</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation */}
      <section className="bg-card border-t">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
            <div>
              <h3 className="text-foreground text-lg font-semibold">
                Ready to implement?
              </h3>
              <p className="text-muted-foreground">
                Check out the documentation and examples to get started.
              </p>
            </div>
            <div className="flex gap-4">
              <a
                href="/docs/THREEJS_HERO_COMPONENT.md"
                className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 transition-colors"
              >
                View Documentation
              </a>
              <a
                href="https://github.com/your-repo/centris-web"
                className="border-border text-foreground hover:bg-muted rounded-lg border px-6 py-2 transition-colors"
              >
                View Source
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
