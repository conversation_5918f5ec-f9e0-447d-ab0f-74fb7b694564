'use client';

import React, { useRef, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  Environment,
  Html,
  useProgress,
  PerspectiveCamera,
} from '@react-three/drei';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';
import { ArrowRight } from 'lucide-react';
import * as THREE from 'three';

import { AnimationConfig } from '@/types';

// Loading component for 3D scene
const Loader = () => {
  const { progress } = useProgress();

  return (
    <Html center>
      <div className="flex flex-col items-center justify-center">
        <div className="border-primary mb-4 h-16 w-16 animate-spin rounded-full border-4 border-t-transparent" />
        <p className="text-foreground text-sm">
          Loading 3D Scene... {Math.round(progress)}%
        </p>
      </div>
    </Html>
  );
};

// Animated object component
const AnimatedObject = ({
  config,
}: {
  config: AnimationConfig['objects'][0];
  index: number;
}) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (!meshRef.current || !config.animation) return;

    const { animation } = config;
    const time = state.clock.getElapsedTime();

    switch (animation.type) {
      case 'rotate':
        if (animation.axis === 'x') {
          meshRef.current.rotation.x = time * animation.speed;
        } else if (animation.axis === 'y') {
          meshRef.current.rotation.y = time * animation.speed;
        } else if (animation.axis === 'z') {
          meshRef.current.rotation.z = time * animation.speed;
        }
        break;

      case 'float':
        meshRef.current.position.y =
          config.position[1] +
          Math.sin(time * animation.speed + (animation.offset || 0)) *
            (animation.amplitude || 0.5);
        break;

      case 'scale':
        const scale =
          1 +
          Math.sin(time * animation.speed + (animation.offset || 0)) *
            (animation.amplitude || 0.1);
        meshRef.current.scale.setScalar(scale);
        break;

      case 'orbit':
        const radius = animation.amplitude || 3;
        const offset = animation.offset || 0;
        meshRef.current.position.x =
          config.position[0] +
          Math.cos(time * animation.speed + offset) * radius;
        meshRef.current.position.z =
          config.position[2] +
          Math.sin(time * animation.speed + offset) * radius;
        break;

      case 'drift':
        meshRef.current.position.x =
          config.position[0] +
          Math.sin(time * animation.speed + (animation.offset || 0)) *
            (animation.amplitude || 0.5);
        meshRef.current.position.y =
          config.position[1] +
          Math.cos(time * animation.speed * 0.7 + (animation.offset || 0)) *
            (animation.amplitude || 0.3);
        break;
    }
  });

  const geometry = useMemo(() => {
    switch (config.type) {
      case 'box':
        return <boxGeometry args={[1, 1, 1]} />;
      case 'sphere':
        return <sphereGeometry args={[0.5, 32, 32]} />;
      case 'plane':
        return <planeGeometry args={[2, 2]} />;
      case 'cylinder':
        return <cylinderGeometry args={[0.5, 0.5, 1, 32]} />;
      case 'torus':
        return <torusGeometry args={[1, 0.3, 16, 100]} />;
      case 'triangle':
        const triangleGeometry = new THREE.ConeGeometry(0.5, 1, 3);
        return <primitive object={triangleGeometry} />;
      case 'octahedron':
        return <octahedronGeometry args={[0.5]} />;
      case 'dodecahedron':
        return <dodecahedronGeometry args={[0.5]} />;
      default:
        return <boxGeometry args={[1, 1, 1]} />;
    }
  }, [config.type]);

  return (
    <mesh
      ref={meshRef}
      position={config.position}
      rotation={config.rotation}
      scale={config.scale}
      castShadow={config.castShadow}
      receiveShadow={config.receiveShadow}
    >
      {geometry}
      <meshStandardMaterial
        color={config.material.color}
        roughness={config.material.roughness || 0.5}
        metalness={config.material.metalness || 0.1}
        transparent={config.material.transparent}
        opacity={config.material.opacity || 1}
        emissive={config.material.emissive || '#000000'}
        emissiveIntensity={config.material.emissiveIntensity || 0}
      />
    </mesh>
  );
};

// Main 3D scene component
const Scene = ({ config }: { config: AnimationConfig }) => {
  return (
    <>
      <PerspectiveCamera
        makeDefault
        position={config.camera.position}
        fov={config.camera.fov || 75}
        near={config.camera.near || 0.1}
        far={config.camera.far || 1000}
      />

      {config.lights.map((light, index) => {
        switch (light.type) {
          case 'ambient':
            return (
              <ambientLight
                key={index}
                color={light.color}
                intensity={light.intensity}
              />
            );
          case 'directional':
            return (
              <directionalLight
                key={index}
                color={light.color}
                intensity={light.intensity}
                position={light.position || [5, 5, 5]}
                castShadow={light.castShadow}
                shadow-mapSize-width={1024}
                shadow-mapSize-height={1024}
              />
            );
          case 'point':
            return (
              <pointLight
                key={index}
                color={light.color}
                intensity={light.intensity}
                position={light.position || [0, 0, 0]}
              />
            );
          default:
            return null;
        }
      })}

      {config.objects.map((obj, index) => (
        <AnimatedObject key={index} config={obj} index={index} />
      ))}

      <Environment preset="studio" />

      {config.scene.fog && (
        <fog
          attach="fog"
          args={[
            config.scene.fog.color,
            config.scene.fog.near,
            config.scene.fog.far,
          ]}
        />
      )}
    </>
  );
};

// CLEAN ThreeJsHero component - NO STATS, NO CONTROLS TEXT
const ThreeJsHero = ({
  animationConfig,
  className = '',
}: {
  animationConfig: AnimationConfig;
  className?: string;
}) => {
  const { theme } = useTheme();

  const sceneBackground = useMemo(() => {
    if (animationConfig.scene.background) {
      return animationConfig.scene.background;
    }
    return theme === 'dark' ? '#f8fafc' : '#f8fafc'; // Always light background
  }, [animationConfig.scene.background, theme]);

  return (
    <section
      className={`relative h-screen w-full overflow-hidden ${className}`}
    >
      <div className="absolute inset-0 z-0">
        <Canvas
          shadows={false}
          dpr={[1, 2]}
          gl={{
            antialias: true,
            powerPreference: 'high-performance',
          }}
          style={{
            background: sceneBackground,
          }}
        >
          <Suspense fallback={<Loader />}>
            <Scene config={animationConfig} />
          </Suspense>
        </Canvas>
      </div>

      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="mx-auto max-w-4xl px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          >
            <h1 className="mb-6 text-4xl leading-tight font-bold text-gray-900 sm:text-5xl md:text-6xl lg:text-7xl xl:text-6xl">
              Revolutionize Your{' '}
              <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                Document Processing
              </span>
            </h1>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
          >
            <p className="mx-auto mb-8 max-w-2xl text-lg leading-relaxed font-medium text-gray-700 sm:text-xl md:text-2xl">
              Centris, the leading-edge electronic document processing system,
              transforms the way you manage and organize all kinds of your
              documents. Say goodbye to manual processing and data extraction,
              and embrace the future with Centris!
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: 'easeOut' }}
          >
            <button className="group inline-flex items-center rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-cyan-700 hover:shadow-xl">
              Contact Us
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </button>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ThreeJsHero;
